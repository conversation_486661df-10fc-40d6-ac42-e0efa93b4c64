<!-- 成绩编辑页面 -->
<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">{{isEdit ? '编辑成绩' : '添加成绩'}}</text>
    <text class="page-subtitle">{{isEdit ? '修改学生课程成绩' : '录入新的学生成绩'}}</text>
  </view>

  <!-- 表单内容 -->
  <view class="form-container">
    <form bindsubmit="onSubmit">
      <!-- 学生信息 -->
      <view class="form-section">
        <view class="section-title">学生信息</view>
        
        <view class="form-item">
          <text class="form-label">学生姓名 <text class="required">*</text></text>
          <input class="form-input" placeholder="请输入学生姓名" 
                 bindinput="onInputChange" data-field="studentName" 
                 value="{{formData.studentName}}" />
        </view>
        
        <view class="form-item">
          <text class="form-label">学号 <text class="required">*</text></text>
          <input class="form-input" placeholder="请输入学号" 
                 bindinput="onInputChange" data-field="studentId" 
                 value="{{formData.studentId}}" />
        </view>
        
        <view class="form-item">
          <text class="form-label">班级</text>
          <picker bindchange="onClassChange" value="{{selectedClassIndex}}" 
                  range="{{classes}}" range-key="name">
            <view class="picker-display">
              <text class="{{formData.className ? '' : 'placeholder'}}">
                {{formData.className || '请选择班级'}}
              </text>
              <image class="dropdown-icon" src="/images/dropdown.png"></image>
            </view>
          </picker>
        </view>
      </view>

      <!-- 课程信息 -->
      <view class="form-section">
        <view class="section-title">课程信息</view>
        
        <view class="form-item">
          <text class="form-label">课程名称 <text class="required">*</text></text>
          <picker bindchange="onCourseChange" value="{{selectedCourseIndex}}" 
                  range="{{courses}}" range-key="name">
            <view class="picker-display">
              <text class="{{formData.courseName ? '' : 'placeholder'}}">
                {{formData.courseName || '请选择课程'}}
              </text>
              <image class="dropdown-icon" src="/images/dropdown.png"></image>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">课程代码</text>
          <input class="form-input" placeholder="自动填充或手动输入" 
                 bindinput="onInputChange" data-field="courseCode" 
                 value="{{formData.courseCode}}" />
        </view>
        
        <view class="form-item">
          <text class="form-label">学分</text>
          <input class="form-input" type="digit" placeholder="请输入学分" 
                 bindinput="onInputChange" data-field="credit" 
                 value="{{formData.credit}}" />
        </view>
        
        <view class="form-item">
          <text class="form-label">任课教师</text>
          <input class="form-input" placeholder="请输入任课教师姓名" 
                 bindinput="onInputChange" data-field="teacher" 
                 value="{{formData.teacher}}" />
        </view>
      </view>

      <!-- 成绩信息 -->
      <view class="form-section">
        <view class="section-title">成绩信息</view>
        
        <view class="form-item">
          <text class="form-label">成绩 <text class="required">*</text></text>
          <input class="form-input score-input" type="digit" placeholder="请输入成绩(0-100)" 
                 bindinput="onScoreChange" value="{{formData.score}}" />
          <view class="score-level">{{gradeLevel}}</view>
        </view>
        
        <view class="form-item">
          <text class="form-label">考试时间</text>
          <picker mode="date" bindchange="onExamDateChange" value="{{formData.examDate}}">
            <view class="picker-display">
              <text class="{{formData.examDate ? '' : 'placeholder'}}">
                {{formData.examDate || '请选择考试时间'}}
              </text>
              <image class="dropdown-icon" src="/images/dropdown.png"></image>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">学期</text>
          <picker bindchange="onSemesterChange" value="{{selectedSemesterIndex}}" 
                  range="{{semesters}}" range-key="name">
            <view class="picker-display">
              <text class="{{formData.semester ? '' : 'placeholder'}}">
                {{formData.semester || '请选择学期'}}
              </text>
              <image class="dropdown-icon" src="/images/dropdown.png"></image>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">备注</text>
          <textarea class="form-textarea" placeholder="请输入备注信息（选填）" 
                    bindinput="onInputChange" data-field="remark" 
                    value="{{formData.remark}}" maxlength="200"></textarea>
          <view class="char-count">{{formData.remark.length}}/200</view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="form-actions">
        <button class="btn-cancel" bindtap="onCancel">取消</button>
        <button class="btn-submit" form-type="submit" disabled="{{!isFormValid}}">
          {{isEdit ? '保存修改' : '添加成绩'}}
        </button>
      </view>
    </form>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{isLoading}}">
  <image class="loading-gif" src="/images/loading.gif"></image>
  <text class="loading-text">{{isEdit ? '保存中...' : '添加中...'}}</text>
</view>
