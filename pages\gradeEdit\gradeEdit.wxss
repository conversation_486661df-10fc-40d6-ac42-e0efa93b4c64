/* 成绩编辑页面样式 */
.container {
  background: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  text-align: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 12rpx;
  display: block;
}

.page-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}

/* 表单容器 */
.form-container {
  margin: -20rpx 30rpx 0;
  position: relative;
  z-index: 1;
}

/* 表单区块 */
.form-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 表单项 */
.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.required {
  color: #ff4d4f;
  margin-left: 4rpx;
}

/* 输入框 */
.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  background: #fafafa;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
  background: white;
}

.form-input.score-input {
  position: relative;
}

.score-level {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #667eea;
  font-weight: 600;
}

/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  background: #fafafa;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #667eea;
  background: white;
}

.char-count {
  text-align: right;
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 选择器 */
.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 24rpx;
  background: #fafafa;
  font-size: 28rpx;
  color: #333;
}

.picker-display .placeholder {
  color: #999;
}

.dropdown-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  gap: 30rpx;
  margin-top: 60rpx;
  padding: 0 30rpx;
}

.btn-cancel,
.btn-submit {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-cancel:active {
  background: #e8e8e8;
}

.btn-submit {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-submit:disabled {
  background: #d9d9d9;
  color: #999;
}

.btn-submit:active:not(:disabled) {
  opacity: 0.8;
}

/* 加载状态 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-gif {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: white;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .form-container {
    margin: -20rpx 20rpx 0;
  }
  
  .form-section {
    padding: 30rpx 20rpx;
  }
  
  .form-actions {
    padding: 0 20rpx;
    gap: 20rpx;
  }
  
  .section-title {
    font-size: 30rpx;
  }
  
  .form-label {
    font-size: 26rpx;
  }
  
  .form-input,
  .form-textarea {
    font-size: 26rpx;
  }
}
