/* 管理员页面样式 */
.container {
  background: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 管理员信息头部 */
.admin-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.admin-info {
  display: flex;
  align-items: center;
}

.admin-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: 24rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.admin-details {
  display: flex;
  flex-direction: column;
}

.admin-name {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
}

.admin-role {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

.login-time {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

.system-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
}

.status-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

.status-value {
  font-size: 26rpx;
  font-weight: 600;
}

.status-value.normal {
  color: #52c41a;
}

.status-value.warning {
  color: #faad14;
}

/* 数据概览 */
.overview-section {
  margin: -20rpx 30rpx 40rpx;
  position: relative;
  z-index: 1;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.overview-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.overview-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.card-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.card-icon image {
  width: 36rpx;
  height: 36rpx;
}

.student-icon {
  background: rgba(24, 144, 255, 0.1);
}

.course-icon {
  background: rgba(82, 196, 26, 0.1);
}

.grade-icon {
  background: rgba(250, 173, 20, 0.1);
}

.teacher-icon {
  background: rgba(114, 46, 209, 0.1);
}

.card-content {
  display: flex;
  flex-direction: column;
}

.card-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.card-label {
  font-size: 24rpx;
  color: #666;
}

/* 快捷操作 */
.quick-actions {
  margin: 0 30rpx 40rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.action-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.action-icon image {
  width: 48rpx;
  height: 48rpx;
}

.grade-manage {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.student-manage {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.course-manage {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.teacher-manage {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.report-center {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.system-settings {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.action-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.action-desc {
  font-size: 22rpx;
  color: #666;
}

/* 最近活动 */
.recent-activities {
  margin: 0 30rpx 40rpx;
}

.activity-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.activity-icon image {
  width: 32rpx;
  height: 32rpx;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.activity-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.activity-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.activity-time {
  font-size: 22rpx;
  color: #999;
}

.view-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
  background: white;
  border-radius: 16rpx;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.view-more text {
  font-size: 26rpx;
  color: #667eea;
  margin-right: 12rpx;
}

.view-more image {
  width: 24rpx;
  height: 24rpx;
}

/* 系统公告 */
.system-notices {
  margin: 0 30rpx 40rpx;
}

.notice-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.notice-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.notice-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.notice-time {
  font-size: 22rpx;
  color: #999;
}

.notice-status {
  background: #ff4d4f;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}

/* 退出登录 */
.logout-section {
  margin: 0 30rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.logout-btn:active {
  background: #d9363e;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.loading-gif {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }

  .overview-card {
    padding: 24rpx;
  }

  .action-item {
    padding: 24rpx;
  }

  .card-number {
    font-size: 28rpx;
  }

  .action-text {
    font-size: 26rpx;
  }
}