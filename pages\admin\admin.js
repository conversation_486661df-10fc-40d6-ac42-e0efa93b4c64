// 管理员页面逻辑
Page({
  data: {
    adminInfo: {},
    lastLoginTime: '',
    systemStatus: 'normal',
    statistics: {
      totalStudents: 0,
      totalCourses: 0,
      totalGrades: 0,
      totalTeachers: 0
    },
    recentActivities: [],
    notices: [],
    isLoading: false
  },

  onLoad: function() {
    this.checkAdminPermission()
    this.initAdminInfo()
    this.loadDashboardData()
  },

  onShow: function() {
    // 页面显示时刷新数据
    this.loadDashboardData()
  },

  // 检查管理员权限
  checkAdminPermission: function() {
    const app = getApp()
    const userInfo = wx.getStorageSync('userInfo') || app.globalData.userInfo

    if (!userInfo || userInfo.role !== 'admin') {
      wx.showToast({
        title: '无管理员权限',
        icon: 'none'
      })
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/grade/grade'
        })
      }, 1500)
      return false
    }
    return true
  },

  // 初始化管理员信息
  initAdminInfo: function() {
    const app = getApp()
    const userInfo = wx.getStorageSync('userInfo') || app.globalData.userInfo || {}
    const lastLogin = wx.getStorageSync('lastLoginTime') || ''

    this.setData({
      adminInfo: userInfo,
      lastLoginTime: this.formatTime(lastLogin)
    })
  },

  // 加载仪表板数据
  loadDashboardData: function() {
    this.setData({ isLoading: true })

    const app = getApp()
    const token = app.globalData.token || wx.getStorageSync('token')

    if (!token) {
      wx.redirectTo({
        url: '/pages/login/login'
      })
      return
    }

    // 并行请求多个接口
    Promise.all([
      this.getStatistics(token),
      this.getRecentActivities(token),
      this.getSystemNotices(token)
    ]).then(() => {
      this.setData({ isLoading: false })
    }).catch(() => {
      this.setData({ isLoading: false })
    })
  },

  // 获取统计数据
  getStatistics: function(token) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'http://127.0.0.1:3000/api/admin/statistics',
        method: 'GET',
        header: {
          'Authorization': 'Bearer ' + token
        },
        success: res => {
          if (res.data && res.data.success) {
            this.setData({
              statistics: res.data.data,
              systemStatus: res.data.systemStatus || 'normal'
            })
            resolve(res.data)
          } else {
            reject(res.data)
          }
        },
        fail: reject
      })
    })
  },

  // 获取最近活动
  getRecentActivities: function(token) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'http://127.0.0.1:3000/api/admin/activities',
        method: 'GET',
        header: {
          'Authorization': 'Bearer ' + token
        },
        data: {
          limit: 5
        },
        success: res => {
          if (res.data && res.data.success) {
            const activities = res.data.data.map(item => ({
              ...item,
              time: this.formatTime(item.createdAt)
            }))
            this.setData({
              recentActivities: activities
            })
            resolve(res.data)
          } else {
            reject(res.data)
          }
        },
        fail: reject
      })
    })
  },

  // 获取系统公告
  getSystemNotices: function(token) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'http://127.0.0.1:3000/api/admin/notices',
        method: 'GET',
        header: {
          'Authorization': 'Bearer ' + token
        },
        data: {
          limit: 3
        },
        success: res => {
          if (res.data && res.data.success) {
            const notices = res.data.data.map(item => ({
              ...item,
              publishTime: this.formatTime(item.publishTime)
            }))
            this.setData({
              notices: notices
            })
            resolve(res.data)
          } else {
            reject(res.data)
          }
        },
        fail: reject
      })
    })
  },

  // 查看全部活动
  viewAllActivities: function() {
    wx.navigateTo({
      url: '/pages/activityLog/activityLog'
    })
  },

  // 查看公告详情
  viewNotice: function(e) {
    const notice = e.currentTarget.dataset.notice
    wx.navigateTo({
      url: `/pages/noticeDetail/noticeDetail?id=${notice.id}`
    })
  },

  // 退出登录
  onLogout: function() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          this.performLogout()
        }
      }
    })
  },

  // 执行退出登录
  performLogout: function() {
    const app = getApp()

    // 清除本地存储
    wx.removeStorageSync('token')
    wx.removeStorageSync('userInfo')

    // 清除全局数据
    app.globalData.token = ''
    app.globalData.userInfo = {}
    app.globalData.isAdmin = false

    // 跳转到登录页
    wx.redirectTo({
      url: '/pages/login/login',
      success: () => {
        wx.showToast({
          title: '已退出登录',
          icon: 'success'
        })
      }
    })
  },

  // 格式化时间
  formatTime: function(timestamp) {
    if (!timestamp) return ''

    const date = new Date(timestamp)
    const now = new Date()
    const diff = now - date

    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前'
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前'
    } else {
      return date.toLocaleDateString()
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadDashboardData()
    wx.stopPullDownRefresh()
  }
})