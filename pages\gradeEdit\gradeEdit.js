// 成绩编辑页面逻辑
Page({
  data: {
    isEdit: false,
    gradeId: '',
    formData: {
      studentName: '',
      studentId: '',
      className: '',
      courseName: '',
      courseCode: '',
      credit: '',
      teacher: '',
      score: '',
      examDate: '',
      semester: '',
      remark: ''
    },
    classes: [
      { id: 'cs2021-1', name: '计算机科学与技术2021-1班' },
      { id: 'cs2021-2', name: '计算机科学与技术2021-2班' },
      { id: 'se2021-1', name: '软件工程2021-1班' },
      { id: 'ai2021-1', name: '人工智能2021-1班' }
    ],
    courses: [
      { id: 'math001', name: '高等数学', code: 'MATH001', credit: 4 },
      { id: 'eng001', name: '大学英语', code: 'ENG001', credit: 3 },
      { id: 'phy001', name: '大学物理', code: 'PHY001', credit: 4 },
      { id: 'cs001', name: '程序设计基础', code: 'CS001', credit: 3 },
      { id: 'cs002', name: '数据结构', code: 'CS002', credit: 4 }
    ],
    semesters: [
      { id: '2024-1', name: '2024年春季学期' },
      { id: '2023-2', name: '2023年秋季学期' },
      { id: '2023-1', name: '2023年春季学期' }
    ],
    selectedClassIndex: -1,
    selectedCourseIndex: -1,
    selectedSemesterIndex: -1,
    gradeLevel: '',
    isFormValid: false,
    isLoading: false
  },

  onLoad: function(options) {
    this.checkAdminPermission()
    
    if (options.id) {
      this.setData({
        isEdit: true,
        gradeId: options.id
      })
      this.loadGradeData(options.id)
    } else {
      // 设置默认值
      this.setData({
        'formData.examDate': this.getCurrentDate(),
        selectedSemesterIndex: 0,
        'formData.semester': this.data.semesters[0].name
      })
    }
  },

  // 检查管理员权限
  checkAdminPermission: function() {
    const app = getApp()
    const userInfo = wx.getStorageSync('userInfo') || app.globalData.userInfo
    
    if (!userInfo || userInfo.role !== 'admin') {
      wx.showToast({
        title: '无管理员权限',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return false
    }
    return true
  },

  // 加载成绩数据（编辑模式）
  loadGradeData: function(id) {
    this.setData({ isLoading: true })
    
    const app = getApp()
    const token = app.globalData.token || wx.getStorageSync('token')
    
    wx.request({
      url: `http://127.0.0.1:3000/api/admin/grades/${id}`,
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + token
      },
      success: res => {
        this.setData({ isLoading: false })
        
        if (res.data && res.data.success) {
          const grade = res.data.data
          
          // 查找对应的索引
          const classIndex = this.data.classes.findIndex(item => item.name === grade.className)
          const courseIndex = this.data.courses.findIndex(item => item.name === grade.courseName)
          const semesterIndex = this.data.semesters.findIndex(item => item.name === grade.semester)
          
          this.setData({
            formData: {
              studentName: grade.studentName || '',
              studentId: grade.studentId || '',
              className: grade.className || '',
              courseName: grade.courseName || '',
              courseCode: grade.courseCode || '',
              credit: grade.credit || '',
              teacher: grade.teacher || '',
              score: grade.score || '',
              examDate: grade.examDate || '',
              semester: grade.semester || '',
              remark: grade.remark || ''
            },
            selectedClassIndex: classIndex >= 0 ? classIndex : -1,
            selectedCourseIndex: courseIndex >= 0 ? courseIndex : -1,
            selectedSemesterIndex: semesterIndex >= 0 ? semesterIndex : -1,
            gradeLevel: this.getGradeLevel(grade.score)
          })
          
          this.validateForm()
        } else {
          wx.showToast({
            title: res.data.message || '获取成绩信息失败',
            icon: 'none'
          })
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        }
      },
      fail: err => {
        this.setData({ isLoading: false })
        console.error('获取成绩信息失败:', err)
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
    })
  },

  // 输入框内容变化
  onInputChange: function(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    
    this.setData({
      [`formData.${field}`]: value
    })
    
    this.validateForm()
  },

  // 成绩输入变化
  onScoreChange: function(e) {
    const score = parseFloat(e.detail.value) || 0
    
    // 限制成绩范围
    if (score > 100) {
      wx.showToast({
        title: '成绩不能超过100分',
        icon: 'none'
      })
      return
    }
    
    this.setData({
      'formData.score': score,
      gradeLevel: this.getGradeLevel(score)
    })
    
    this.validateForm()
  },

  // 班级选择变化
  onClassChange: function(e) {
    const index = e.detail.value
    const selectedClass = this.data.classes[index]
    
    this.setData({
      selectedClassIndex: index,
      'formData.className': selectedClass.name
    })
    
    this.validateForm()
  },

  // 课程选择变化
  onCourseChange: function(e) {
    const index = e.detail.value
    const selectedCourse = this.data.courses[index]
    
    this.setData({
      selectedCourseIndex: index,
      'formData.courseName': selectedCourse.name,
      'formData.courseCode': selectedCourse.code,
      'formData.credit': selectedCourse.credit
    })
    
    this.validateForm()
  },

  // 学期选择变化
  onSemesterChange: function(e) {
    const index = e.detail.value
    const selectedSemester = this.data.semesters[index]
    
    this.setData({
      selectedSemesterIndex: index,
      'formData.semester': selectedSemester.name
    })
    
    this.validateForm()
  },

  // 考试时间选择变化
  onExamDateChange: function(e) {
    this.setData({
      'formData.examDate': e.detail.value
    })
  },

  // 表单验证
  validateForm: function() {
    const { studentName, studentId, courseName, score } = this.data.formData
    const isValid = studentName && studentId && courseName && score !== ''
    
    this.setData({
      isFormValid: isValid
    })
  },

  // 获取成绩等级
  getGradeLevel: function(score) {
    if (!score) return ''
    if (score >= 90) return '优秀'
    if (score >= 80) return '良好'
    if (score >= 70) return '中等'
    if (score >= 60) return '及格'
    return '不及格'
  },

  // 获取当前日期
  getCurrentDate: function() {
    const date = new Date()
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // 取消操作
  onCancel: function() {
    wx.navigateBack()
  },

  // 提交表单
  onSubmit: function() {
    if (!this.data.isFormValid) {
      wx.showToast({
        title: '请填写必填项',
        icon: 'none'
      })
      return
    }
    
    this.setData({ isLoading: true })
    
    const app = getApp()
    const token = app.globalData.token || wx.getStorageSync('token')
    
    const url = this.data.isEdit 
      ? `http://127.0.0.1:3000/api/admin/grades/${this.data.gradeId}`
      : 'http://127.0.0.1:3000/api/admin/grades'
    
    const method = this.data.isEdit ? 'PUT' : 'POST'
    
    wx.request({
      url: url,
      method: method,
      header: {
        'Authorization': 'Bearer ' + token
      },
      data: {
        ...this.data.formData,
        gradeLevel: this.data.gradeLevel
      },
      success: res => {
        this.setData({ isLoading: false })
        
        if (res.data && res.data.success) {
          wx.showToast({
            title: this.data.isEdit ? '修改成功' : '添加成功',
            icon: 'success'
          })
          
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: res.data.message || (this.data.isEdit ? '修改失败' : '添加失败'),
            icon: 'none'
          })
        }
      },
      fail: err => {
        this.setData({ isLoading: false })
        console.error('提交失败:', err)
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
    })
  }
})
